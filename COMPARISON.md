# Jellyfin MCP Server: Python vs JavaScript 版本对比

## 概述

本项目提供了两个功能完全相同的 Jellyfin MCP Server 实现：
- **Python 版本**: 使用 Python 3.8+ 和 asyncio
- **JavaScript 版本**: 使用 Node.js 18+ 和现代 ES 模块

## 功能对比

| 功能 | Python 版本 | JavaScript 版本 | 说明 |
|------|-------------|-----------------|------|
| MCP 协议支持 | ✅ | ✅ | 完全兼容 |
| STDIO 通信 | ✅ | ✅ | 标准输入输出模式 |
| 搜索电影 | ✅ | ✅ | search_movies 工具 |
| 搜索电视剧 | ✅ | ✅ | search_tv_shows 工具 |
| 搜索所有媒体 | ✅ | ✅ | search_all_media 工具 |
| 获取媒体详情 | ✅ | ✅ | get_media_details 工具 |
| URL 生成 | ✅ | ✅ | 播放和流媒体 URL |
| 环境变量配置 | ✅ | ✅ | .env 文件支持 |
| 错误处理 | ✅ | ✅ | 完善的异常处理 |
| 日志记录 | ✅ | ✅ | 详细的调试信息 |

## 技术架构对比

### Python 版本
```
src/jellyfin_mcp/
├── __init__.py
└── server.py          # 单文件实现
run_server.py           # 启动脚本
test_server.py          # 测试脚本
pyproject.toml          # 项目配置
```

### JavaScript 版本
```
js-version/src/
├── config.js           # 配置管理
├── jellyfin-client.js  # API 客户端
└── server.js           # MCP 服务器
test/test.js            # 测试脚本
package.json            # 项目配置
```

## 依赖对比

### Python 版本
```python
# 核心依赖
mcp >= 1.0.0           # MCP 协议库
httpx >= 0.25.0        # HTTP 客户端
pydantic >= 2.0.0      # 数据验证
```

### JavaScript 版本
```json
{
  "@modelcontextprotocol/sdk": "^0.5.0",  // MCP SDK
  "axios": "^1.6.0",                      // HTTP 客户端
  "dotenv": "^16.3.0"                     // 环境变量
}
```

## 性能对比

| 指标 | Python 版本 | JavaScript 版本 | 优势 |
|------|-------------|-----------------|------|
| 启动时间 | ~2-3 秒 | ~0.5-1 秒 | JS 更快 |
| 内存占用 | ~50-80 MB | ~30-50 MB | JS 更低 |
| 并发处理 | asyncio 事件循环 | Node.js 事件循环 | 相当 |
| HTTP 性能 | httpx (优秀) | axios (优秀) | 相当 |

## 开发体验对比

### Python 版本优势
- **类型安全**: Pydantic 提供强类型验证
- **生态成熟**: Python 在数据处理领域更成熟
- **调试工具**: 丰富的调试和分析工具
- **文档完善**: Python 社区文档更完善

### JavaScript 版本优势
- **现代语法**: ES6+ 模块和语法特性
- **包管理**: npm 生态系统更活跃
- **开发速度**: 更快的开发迭代周期
- **部署简单**: 单一运行时环境

## 部署对比

### Python 版本
```bash
# 安装依赖
pip install -e .

# 运行服务器
python run_server.py

# 或使用模块方式
python -m jellyfin_mcp.server
```

### JavaScript 版本
```bash
# 安装依赖
npm install

# 运行服务器
npm start

# 或直接运行
node src/server.js
```

## MCP 客户端配置对比

### Python 版本配置
```json
{
  "mcpServers": {
    "jellyfin": {
      "command": "python",
      "args": ["/path/to/run_server.py"],
      "env": {
        "JELLYFIN_SERVER_URL": "http://localhost:8096",
        "JELLYFIN_API_KEY": "your_api_key"
      }
    }
  }
}
```

### JavaScript 版本配置
```json
{
  "mcpServers": {
    "jellyfin-js": {
      "command": "node",
      "args": ["/path/to/js-version/src/server.js"],
      "env": {
        "JELLYFIN_SERVER_URL": "http://localhost:8096",
        "JELLYFIN_API_KEY": "your_api_key"
      }
    }
  }
}
```

## 代码风格对比

### Python 版本特点
- **面向对象**: 使用类和继承
- **类型注解**: 完整的类型提示
- **异步编程**: async/await 语法
- **数据验证**: Pydantic 模型

```python
class MediaItem(BaseModel):
    id: str
    name: str
    type: str
    year: Optional[int] = None
```

### JavaScript 版本特点
- **函数式**: 更多函数式编程特性
- **ES 模块**: 现代模块系统
- **异步编程**: Promise 和 async/await
- **灵活性**: 动态类型系统

```javascript
export class MediaItem {
    constructor(data) {
        this.id = data.Id;
        this.name = data.Name;
        this.type = data.Type;
    }
}
```

## 选择建议

### 选择 Python 版本，如果你：
- 更熟悉 Python 生态系统
- 需要强类型验证和数据安全
- 项目中已有 Python 基础设施
- 需要与其他 Python 工具集成

### 选择 JavaScript 版本，如果你：
- 更熟悉 Node.js 生态系统
- 需要更快的启动速度和更低的内存占用
- 项目中已有 Node.js 基础设施
- 偏好现代 JavaScript 语法

## 维护和扩展

两个版本都采用了模块化设计，易于维护和扩展：

- **配置管理**: 独立的配置模块
- **API 客户端**: 独立的 Jellyfin 客户端
- **错误处理**: 统一的错误处理机制
- **日志记录**: 完善的日志系统

## 总结

两个版本在功能上完全等价，主要差异在于：
- **性能**: JavaScript 版本启动更快，内存占用更低
- **生态**: Python 版本在数据处理方面更成熟
- **开发**: 根据团队技术栈选择合适的版本

建议根据你的具体需求和技术背景选择合适的版本。
