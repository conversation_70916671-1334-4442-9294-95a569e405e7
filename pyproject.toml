[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "jellyfin-mcp"
version = "0.1.0"
description = "MCP server for querying Jellyfin media server"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "mcp>=1.0.0",
    "httpx>=0.25.0",
    "pydantic>=2.0.0",
]

[project.scripts]
jellyfin-mcp = "jellyfin_mcp.server:main"

[tool.hatch.build.targets.wheel]
packages = ["src/jellyfin_mcp"]

[tool.hatch.envs.default]
dependencies = [
    "pytest",
    "pytest-asyncio",
]
