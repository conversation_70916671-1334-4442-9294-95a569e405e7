#!/usr/bin/env python3
"""
Jellyfin MCP Server Runner

This script provides an easy way to run the Jellyfin MCP server with proper error handling.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from jellyfin_mcp.server import main

def load_env_file():
    """Load environment variables from .env file if it exists."""
    env_file = Path(__file__).parent / ".env"
    if env_file.exists():
        print(f"Loading environment from {env_file}")
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()

def check_config():
    """Check if required configuration is present."""
    required_vars = ["JELLYFIN_SERVER_URL", "JELLYFIN_API_KEY"]
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these variables or create a .env file based on .env.example")
        return False

    print("✅ Configuration looks good!")
    print(f"   Server URL: {os.getenv('JELLYFIN_SERVER_URL')}")
    print(f"   User ID: {os.getenv('JELLYFIN_USER_ID', 'Not set (using server-wide content)')}")
    return True

if __name__ == "__main__":
    print("🎬 Jellyfin MCP Server")
    print("=" * 50)

    # Load .env file if it exists
    load_env_file()

    # Check configuration
    if not check_config():
        sys.exit(1)

    print("\n🚀 Starting MCP server...")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)
