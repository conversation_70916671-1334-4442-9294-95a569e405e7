#!/usr/bin/env python3
"""
Jellyfin MCP Server

A Model Context Protocol server that provides tools for querying Jellyfin media server
to search for movies and TV shows, and retrieve their streaming URLs.
"""

import asyncio
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional, Sequence
from urllib.parse import urljoin, quote

import httpx
from mcp.server import Server
from mcp.server.models import InitializationOptions
# 兼容 mcp 没有 NotificationOptions 的情况
try:
    from mcp.server.models import NotificationOptions
except ImportError:
    class NotificationOptions:
        def __init__(self):
            self.tools_changed = None
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ServerCapabilities,
    TextContent,
    Tool,
)
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("jellyfin-mcp")

class JellyfinConfig(BaseModel):
    """Jellyfin server configuration."""
    server_url: str = Field(..., description="Jellyfin server URL")
    api_key: str = Field(..., description="Jellyfin API key")

class MediaItem(BaseModel):
    """Represents a media item from Jellyfin."""
    id: str
    name: str
    type: str
    year: Optional[int] = None
    overview: Optional[str] = None
    genres: List[str] = []
    runtime_ticks: Optional[int] = None
    play_url: Optional[str] = None
    stream_url: Optional[str] = None

class JellyfinClient:
    """Client for interacting with Jellyfin API."""

    def __init__(self, config: JellyfinConfig):
        self.config = config
        self.client = httpx.AsyncClient(timeout=30.0)
        self.headers = {
            "X-Emby-Token": config.api_key,
            "Content-Type": "application/json"
        }

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()

    def _build_url(self, endpoint: str) -> str:
        """Build full URL for API endpoint."""
        return urljoin(self.config.server_url.rstrip('/') + '/', endpoint.lstrip('/'))

    async def search_media(self, query: str, media_types: List[str] = None) -> List[MediaItem]:
        """Search for media items by name."""
        if media_types is None:
            media_types = ["Movie", "Series"]

        # Use Jellyfin's Items API for search
        params = {
            "searchTerm": query,
            "IncludeItemTypes": ",".join(media_types),
            "Limit": 20,
            "Recursive": True,
            "EnableTotalRecordCount": False
        }

        url = self._build_url("/Items")

        try:
            logger.info(f"Searching Jellyfin: {url} with params: {params}")
            response = await self.client.get(url, headers=self.headers, params=params)
            logger.info(f"Jellyfin response status: {response.status_code}")
            response.raise_for_status()
            data = response.json()
            logger.info(f"Jellyfin response keys: {list(data.keys())}")

            items = []
            # Handle Items response format
            search_items = data.get("Items", [])

            for item in search_items:
                media_item = MediaItem(
                    id=item["Id"],
                    name=item["Name"],
                    type=item["Type"],
                    year=item.get("ProductionYear"),
                    overview=item.get("Overview"),
                    genres=item.get("Genres", []),
                    runtime_ticks=item.get("RunTimeTicks")
                )

                # Generate URLs
                media_item.play_url = self._get_play_url(item["Id"])
                media_item.stream_url = self._get_stream_url(item["Id"])

                items.append(media_item)

            return items

        except httpx.HTTPError as e:
            logger.error(f"HTTP error searching media: {e}")
            raise
        except Exception as e:
            logger.error(f"Error searching media: {e}")
            raise

    async def get_media_details(self, item_id: str) -> Optional[MediaItem]:
        """Get detailed information about a specific media item."""
        params = {
            "Fields": "Overview,Genres,RunTimeTicks,MediaSources"
        }

        url = self._build_url(f"/Items/{item_id}")

        try:
            response = await self.client.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            item = response.json()

            media_item = MediaItem(
                id=item["Id"],
                name=item["Name"],
                type=item["Type"],
                year=item.get("ProductionYear"),
                overview=item.get("Overview"),
                genres=item.get("Genres", []),
                runtime_ticks=item.get("RunTimeTicks")
            )

            # Generate URLs
            media_item.play_url = self._get_play_url(item["Id"])
            media_item.stream_url = self._get_stream_url(item["Id"])

            return media_item

        except httpx.HTTPError as e:
            logger.error(f"HTTP error getting media details: {e}")
            return None
        except Exception as e:
            logger.error(f"Error getting media details: {e}")
            return None

    def _get_play_url(self, item_id: str) -> str:
        """Generate play URL for a media item."""
        base_url = self.config.server_url.rstrip('/')
        return f"{base_url}/web/index.html#!/details?id={item_id}"

    def _get_stream_url(self, item_id: str) -> str:
        """Generate direct stream URL for a media item."""
        base_url = self.config.server_url.rstrip('/')
        stream_url = f"{base_url}/Videos/{item_id}/stream"

        params = [
            f"api_key={self.config.api_key}",
            "Container=mp4,mkv,avi,mov,wmv,flv,webm",
            "VideoCodec=h264,hevc,vp8,vp9",
            "AudioCodec=aac,mp3,ac3,eac3,flac,vorbis"
        ]

        return f"{stream_url}?{'&'.join(params)}"

# Global Jellyfin client
jellyfin_client: Optional[JellyfinClient] = None

def load_config() -> JellyfinConfig:
    """Load configuration from environment variables."""
    server_url = os.getenv("JELLYFIN_SERVER_URL")
    api_key = os.getenv("JELLYFIN_API_KEY")

    if not server_url:
        raise ValueError("JELLYFIN_SERVER_URL environment variable is required")
    if not api_key:
        raise ValueError("JELLYFIN_API_KEY environment variable is required")

    return JellyfinConfig(
        server_url=server_url,
        api_key=api_key,
    )

# Create the MCP server
server = Server("jellyfin-mcp")

@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """List available tools."""
    return [
        Tool(
            name="search_movies",
            description="Search for movies on Jellyfin server",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query for movie titles"
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="search_tv_shows",
            description="Search for TV shows/series on Jellyfin server",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query for TV show titles"
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="search_all_media",
            description="Search for both movies and TV shows on Jellyfin server",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query for media titles"
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="get_media_details",
            description="Get detailed information about a specific media item",
            inputSchema={
                "type": "object",
                "properties": {
                    "item_id": {
                        "type": "string",
                        "description": "Jellyfin item ID"
                    }
                },
                "required": ["item_id"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> List[TextContent]:
    """Handle tool calls."""
    global jellyfin_client

    if jellyfin_client is None:
        return [TextContent(
            type="text",
            text="Error: Jellyfin client not initialized. Please check your configuration."
        )]

    try:
        if name == "search_movies":
            query = arguments.get("query", "")
            if not query:
                return [TextContent(type="text", text="Error: Query parameter is required")]

            results = await jellyfin_client.search_media(query, ["Movie"])

            if not results:
                return [TextContent(type="text", text=f"No movies found for query: {query}")]

            response = f"Found {len(results)} movie(s) for '{query}':\n\n"
            for item in results:
                response += f"**{item.name}**"
                if item.year:
                    response += f" ({item.year})"
                response += f"\n- Type: {item.type}\n"
                response += f"- ID: {item.id}\n"
                if item.genres:
                    response += f"- Genres: {', '.join(item.genres)}\n"
                if item.overview:
                    response += f"- Overview: {item.overview[:200]}{'...' if len(item.overview) > 200 else ''}\n"
                response += f"- Play URL: {item.play_url}\n"
                response += f"- Stream URL: {item.stream_url}\n\n"

            return [TextContent(type="text", text=response)]

        elif name == "search_tv_shows":
            query = arguments.get("query", "")
            if not query:
                return [TextContent(type="text", text="Error: Query parameter is required")]

            results = await jellyfin_client.search_media(query, ["Series"])

            if not results:
                return [TextContent(type="text", text=f"No TV shows found for query: {query}")]

            response = f"Found {len(results)} TV show(s) for '{query}':\n\n"
            for item in results:
                response += f"**{item.name}**"
                if item.year:
                    response += f" ({item.year})"
                response += f"\n- Type: {item.type}\n"
                response += f"- ID: {item.id}\n"
                if item.genres:
                    response += f"- Genres: {', '.join(item.genres)}\n"
                if item.overview:
                    response += f"- Overview: {item.overview[:200]}{'...' if len(item.overview) > 200 else ''}\n"
                response += f"- Play URL: {item.play_url}\n"
                response += f"- Stream URL: {item.stream_url}\n\n"

            return [TextContent(type="text", text=response)]

        elif name == "search_all_media":
            query = arguments.get("query", "")
            if not query:
                return [TextContent(type="text", text="Error: Query parameter is required")]

            results = await jellyfin_client.search_media(query, ["Movie", "Series"])

            if not results:
                return [TextContent(type="text", text=f"No media found for query: {query}")]

            response = f"Found {len(results)} media item(s) for '{query}':\n\n"
            for item in results:
                response += f"**{item.name}**"
                if item.year:
                    response += f" ({item.year})"
                response += f"\n- Type: {item.type}\n"
                response += f"- ID: {item.id}\n"
                if item.genres:
                    response += f"- Genres: {', '.join(item.genres)}\n"
                if item.overview:
                    response += f"- Overview: {item.overview[:200]}{'...' if len(item.overview) > 200 else ''}\n"
                response += f"- Play URL: {item.play_url}\n"
                response += f"- Stream URL: {item.stream_url}\n\n"

            return [TextContent(type="text", text=response)]

        elif name == "get_media_details":
            item_id = arguments.get("item_id", "")
            if not item_id:
                return [TextContent(type="text", text="Error: item_id parameter is required")]

            item = await jellyfin_client.get_media_details(item_id)

            if not item:
                return [TextContent(type="text", text=f"No media item found with ID: {item_id}")]

            response = f"**{item.name}**"
            if item.year:
                response += f" ({item.year})"
            response += f"\n- Type: {item.type}\n"
            response += f"- ID: {item.id}\n"
            if item.genres:
                response += f"- Genres: {', '.join(item.genres)}\n"
            if item.runtime_ticks:
                runtime_minutes = item.runtime_ticks // 600000000  # Convert ticks to minutes
                response += f"- Runtime: {runtime_minutes} minutes\n"
            if item.overview:
                response += f"- Overview: {item.overview}\n"
            response += f"- Play URL: {item.play_url}\n"
            response += f"- Stream URL: {item.stream_url}\n"

            return [TextContent(type="text", text=response)]

        else:
            return [TextContent(type="text", text=f"Unknown tool: {name}")]

    except Exception as e:
        logger.error(f"Error in tool {name}: {e}")
        return [TextContent(type="text", text=f"Error: {str(e)}")]

async def main():
    """Main entry point for the MCP server."""
    global jellyfin_client

    try:
        # Load configuration
        logger.info("Loading configuration...")
        config = load_config()
        logger.info(f"Configuration loaded successfully")

        # Initialize Jellyfin client
        logger.info("Initializing Jellyfin client...")
        jellyfin_client = JellyfinClient(config)
        logger.info(f"Starting Jellyfin MCP server with server URL: {config.server_url}")

        # Run the server
        logger.info("Starting MCP server...")
        try:
            async with stdio_server() as (read_stream, write_stream):
                await server.run(
                    read_stream,
                    write_stream,
                    InitializationOptions(
                        server_name="jellyfin-mcp",
                        server_version="0.1.0",
                        capabilities=server.get_capabilities(
                            notification_options=NotificationOptions(),
                            experimental_capabilities=None,
                        ),
                    ),
                )
        finally:
            # Cleanup
            if jellyfin_client:
                logger.info("Closing Jellyfin client...")
                await jellyfin_client.client.aclose()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Failed to start server: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
