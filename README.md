# Jellyfin MCP Server

一个用于查询 Jellyfin 媒体服务器的 Model Context Protocol (MCP) 服务器。该服务器提供了搜索电影和电视剧的工具，并返回播放 URL 和流媒体 URL。

## 功能特性

- 🎬 搜索电影
- 📺 搜索电视剧
- 🔍 搜索所有媒体类型
- 📋 获取媒体详细信息
- 🔗 生成播放 URL 和直接流媒体 URL
- 🔄 支持 STDIO 模式的 MCP 协议

## 安装

### 使用 pip 安装

```bash
pip install -e .
```

### 从源码安装

```bash
git clone <repository-url>
cd jellyfin-mcp
pip install -e .
```

## 配置

### 环境变量

创建 `.env` 文件或设置以下环境变量：

```bash
# 必需：Jellyfin 服务器 URL
export JELLYFIN_SERVER_URL="http://localhost:8096"

# 必需：Jellyfin API 密钥
export JELLYFIN_API_KEY="your_api_key_here"

```

### 获取 Jellyfin API 密钥

1. 登录到 Jellyfin 管理面板
2. 导航到 **Dashboard** > **API Keys**
3. 点击 **+** 创建新的 API 密钥
4. 复制生成的 API 密钥

## 使用方法

### 作为 MCP 服务器运行

#### 方法 1：使用启动脚本（推荐）

```bash
python run_server.py
```

#### 方法 2：使用 pip 安装的命令

```bash
jellyfin-mcp
```

#### 方法 3：直接运行 Python 模块

```bash
python -m jellyfin_mcp.server
```

### 在 MCP 客户端中配置

#### Claude Desktop 配置

在 Claude Desktop 的配置文件中添加以下内容：

```json
{
  "mcpServers": {
    "jellyfin": {
      "command": "python",
      "args": ["/path/to/jellyfin-mcp/run_server.py"],
      "env": {
        "JELLYFIN_SERVER_URL": "http://localhost:8096",
        "JELLYFIN_API_KEY": "your_api_key_here"
      }
    }
  }
}
```

**注意：** 请将 `/path/to/jellyfin-mcp/run_server.py` 替换为实际的文件路径。

#### 其他 MCP 客户端

参考 `mcp-config-example.json` 文件中的配置示例。

### 可用工具

#### 1. search_movies
搜索电影

**参数：**
- `query` (string): 电影标题搜索查询

**示例：**
```json
{
  "name": "search_movies",
  "arguments": {
    "query": "The Matrix"
  }
}
```

#### 2. search_tv_shows
搜索电视剧

**参数：**
- `query` (string): 电视剧标题搜索查询

**示例：**
```json
{
  "name": "search_tv_shows",
  "arguments": {
    "query": "Breaking Bad"
  }
}
```

#### 3. search_all_media
搜索所有媒体类型（电影和电视剧）

**参数：**
- `query` (string): 媒体标题搜索查询

**示例：**
```json
{
  "name": "search_all_media",
  "arguments": {
    "query": "Star Wars"
  }
}
```

#### 4. get_media_details
获取特定媒体项目的详细信息

**参数：**
- `item_id` (string): Jellyfin 项目 ID

**示例：**
```json
{
  "name": "get_media_details",
  "arguments": {
    "item_id": "abc123def456"
  }
}
```

## 返回的 URL 类型

### Play URL
用于在 Jellyfin Web 界面中播放媒体的 URL。格式：
```
http://your-jellyfin-server/web/index.html#!/details?id=ITEM_ID
```

### Stream URL
用于直接流媒体播放的 URL，包含适当的编解码器参数。格式：
```
http://your-jellyfin-server/Videos/ITEM_ID/stream?api_key=API_KEY&Container=mp4,mkv...
```

## 开发

### 项目结构

```
jellyfin-mcp/
├── src/
│   └── jellyfin_mcp/
│       ├── __init__.py
│       └── server.py
├── pyproject.toml
├── .env.example
└── README.md
```

### 依赖项

- `mcp>=1.0.0` - Model Context Protocol 库
- `httpx>=0.25.0` - HTTP 客户端
- `pydantic>=2.0.0` - 数据验证

### 运行测试

```bash
pip install -e ".[dev]"
pytest
```

### 验证安装

运行测试脚本来确保一切正常：

```bash
python test_fix.py
```

## 故障排除

### 常见问题

1. **TaskGroup 错误**
   - 如果遇到 "unhandled errors in a TaskGroup" 错误，请确保：
     - 使用最新版本的代码
     - 正确设置了环境变量
     - 运行 `python test_fix.py` 验证修复

2. **连接错误**
   - 确保 Jellyfin 服务器正在运行且可访问
   - 检查 `JELLYFIN_SERVER_URL` 是否正确

3. **认证错误**
   - 验证 `JELLYFIN_API_KEY` 是否有效
   - 确保 API 密钥具有适当的权限

4. **搜索结果为空**
   - 检查搜索查询是否正确
   - 确保 Jellyfin 服务器中有相应的媒体内容

### 日志

服务器会输出详细的日志信息，包括：
- 启动信息
- API 请求和响应
- 错误信息

## 许可证

MIT License

## 贡献

欢迎提交 Pull Request 和 Issue！
