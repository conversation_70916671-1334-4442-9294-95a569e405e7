#!/usr/bin/env node

/**
 * Configuration management for Jellyfin MCP Server
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';

// Load environment variables from .env file if it exists
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');
const envPath = join(projectRoot, '.env');

if (existsSync(envPath)) {
    console.log(`Loading environment from ${envPath}`);
    dotenv.config({ path: envPath });
}

/**
 * Jellyfin server configuration
 */
export class JellyfinConfig {
    constructor() {
        this.serverUrl = process.env.JELLYFIN_SERVER_URL;
        this.apiKey = process.env.JELLYFIN_API_KEY;

        this.validate();
    }

    /**
     * Validate required configuration
     */
    validate() {
        if (!this.serverUrl) {
            throw new Error('JELLYFIN_SERVER_URL environment variable is required');
        }

        if (!this.apiKey) {
            throw new Error('JELLYFIN_API_KEY environment variable is required');
        }

        // Ensure server URL doesn't end with slash
        this.serverUrl = this.serverUrl.replace(/\/$/, '');
    }
}

/**
 * Load and validate configuration
 * @returns {JellyfinConfig} Validated configuration object
 */
export function loadConfig() {
    return new JellyfinConfig();
}
