#!/usr/bin/env node

/**
 * Jellyfin MCP Server (JavaScript Version)
 *
 * A Model Context Protocol server that provides tools for querying Jellyfin media server
 * to search for movies and TV shows, and retrieve their streaming URLs.
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
    CallToolRequestSchema,
    ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

import { loadConfig } from './config.js';
import { JellyfinClient } from './jellyfin-client.js';

// Global Jellyfin client
let jellyfinClient = null;

/**
 * Format media item for response
 * @param {MediaItem} item - Media item
 * @returns {string} Formatted response
 */
function formatMediaItem(item) {
    let response = `**${item.name}**`;
    if (item.year) {
        response += ` (${item.year})`;
    }
    response += `\n- Type: ${item.type}\n`;
    response += `- ID: ${item.id}\n`;

    if (item.genres && item.genres.length > 0) {
        response += `- Genres: ${item.genres.join(', ')}\n`;
    }

    if (item.overview) {
        const truncatedOverview = item.overview.length > 200
            ? item.overview.substring(0, 200) + '...'
            : item.overview;
        response += `- Overview: ${truncatedOverview}\n`;
    }

    response += `- Play URL: ${item.playUrl}\n`;
    response += `- Stream URL: ${item.streamUrl}\n`;

    return response;
}

/**
 * Create and configure the MCP server
 */
async function createServer() {
    const server = new Server(
        {
            name: 'jellyfin-mcp-js',
            version: '0.1.0',
        },
        {
            capabilities: {
                tools: {},
            },
        }
    );

    // List available tools
    server.setRequestHandler(ListToolsRequestSchema, async () => {
        return {
            tools: [
                {
                    name: 'search_movies',
                    description: 'Search for movies on Jellyfin server',
                    inputSchema: {
                        type: 'object',
                        properties: {
                            query: {
                                type: 'string',
                                description: 'Search query for movie titles'
                            }
                        },
                        required: ['query']
                    }
                },
                {
                    name: 'search_tv_shows',
                    description: 'Search for TV shows/series on Jellyfin server',
                    inputSchema: {
                        type: 'object',
                        properties: {
                            query: {
                                type: 'string',
                                description: 'Search query for TV show titles'
                            }
                        },
                        required: ['query']
                    }
                },
                {
                    name: 'search_all_media',
                    description: 'Search for both movies and TV shows on Jellyfin server',
                    inputSchema: {
                        type: 'object',
                        properties: {
                            query: {
                                type: 'string',
                                description: 'Search query for media titles'
                            }
                        },
                        required: ['query']
                    }
                },
                {
                    name: 'get_media_details',
                    description: 'Get detailed information about a specific media item',
                    inputSchema: {
                        type: 'object',
                        properties: {
                            item_id: {
                                type: 'string',
                                description: 'Jellyfin item ID'
                            }
                        },
                        required: ['item_id']
                    }
                }
            ]
        };
    });

    // Handle tool calls
    server.setRequestHandler(CallToolRequestSchema, async (request) => {
        const { name, arguments: args } = request.params;

        if (!jellyfinClient) {
            return {
                content: [
                    {
                        type: 'text',
                        text: 'Error: Jellyfin client not initialized. Please check your configuration.'
                    }
                ]
            };
        }

        try {
            switch (name) {
                case 'search_movies': {
                    const query = args.query;
                    if (!query) {
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: 'Error: Query parameter is required'
                                }
                            ]
                        };
                    }

                    const results = await jellyfinClient.searchMedia(query, ['Movie']);

                    if (results.length === 0) {
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: `No movies found for query: ${query}`
                                }
                            ]
                        };
                    }

                    let response = `Found ${results.length} movie(s) for '${query}':\n\n`;
                    for (const item of results) {
                        response += formatMediaItem(item) + '\n';
                    }

                    return {
                        content: [
                            {
                                type: 'text',
                                text: response
                            }
                        ]
                    };
                }

                case 'search_tv_shows': {
                    const query = args.query;
                    if (!query) {
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: 'Error: Query parameter is required'
                                }
                            ]
                        };
                    }

                    const results = await jellyfinClient.searchMedia(query, ['Series']);

                    if (results.length === 0) {
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: `No TV shows found for query: ${query}`
                                }
                            ]
                        };
                    }

                    let response = `Found ${results.length} TV show(s) for '${query}':\n\n`;
                    for (const item of results) {
                        response += formatMediaItem(item) + '\n';
                    }

                    return {
                        content: [
                            {
                                type: 'text',
                                text: response
                            }
                        ]
                    };
                }

                case 'search_all_media': {
                    const query = args.query;
                    if (!query) {
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: 'Error: Query parameter is required'
                                }
                            ]
                        };
                    }

                    const results = await jellyfinClient.searchMedia(query, ['Movie', 'Series']);

                    if (results.length === 0) {
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: `No media found for query: ${query}`
                                }
                            ]
                        };
                    }

                    let response = `Found ${results.length} media item(s) for '${query}':\n\n`;
                    for (const item of results) {
                        response += formatMediaItem(item) + '\n';
                    }

                    return {
                        content: [
                            {
                                type: 'text',
                                text: response
                            }
                        ]
                    };
                }

                case 'get_media_details': {
                    const itemId = args.item_id;
                    if (!itemId) {
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: 'Error: item_id parameter is required'
                                }
                            ]
                        };
                    }

                    const item = await jellyfinClient.getMediaDetails(itemId);

                    if (!item) {
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: `No media item found with ID: ${itemId}`
                                }
                            ]
                        };
                    }

                    let response = `**${item.name}**`;
                    if (item.year) {
                        response += ` (${item.year})`;
                    }
                    response += `\n- Type: ${item.type}\n`;
                    response += `- ID: ${item.id}\n`;

                    if (item.genres && item.genres.length > 0) {
                        response += `- Genres: ${item.genres.join(', ')}\n`;
                    }

                    const runtimeMinutes = item.getRuntimeMinutes();
                    if (runtimeMinutes) {
                        response += `- Runtime: ${runtimeMinutes} minutes\n`;
                    }

                    if (item.overview) {
                        response += `- Overview: ${item.overview}\n`;
                    }

                    response += `- Play URL: ${item.playUrl}\n`;
                    response += `- Stream URL: ${item.streamUrl}\n`;

                    return {
                        content: [
                            {
                                type: 'text',
                                text: response
                            }
                        ]
                    };
                }

                default:
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `Unknown tool: ${name}`
                            }
                        ]
                    };
            }
        } catch (error) {
            console.error(`Error in tool ${name}:`, error);
            return {
                content: [
                    {
                        type: 'text',
                        text: `Error: ${error.message}`
                    }
                ]
            };
        }
    });

    return server;
}

/**
 * Main entry point
 */
export async function main() {
    try {
        console.log('Loading configuration...');
        const config = loadConfig();
        console.log('Configuration loaded successfully');

        console.log('Initializing Jellyfin client...');
        jellyfinClient = new JellyfinClient(config);
        console.log(`Starting Jellyfin MCP server with server URL: ${config.serverUrl}`);

        console.log('Starting MCP server...');
        const server = await createServer();
        const transport = new StdioServerTransport();
        await server.connect(transport);

        console.log('Jellyfin MCP Server is running...');

    } catch (error) {
        if (error.message.includes('environment variable')) {
            console.error('Configuration error:', error.message);
        } else {
            console.error('Failed to start server:', error);
        }
        process.exit(1);
    }
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\nServer stopped by user');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\nServer terminated');
    process.exit(0);
});

// Start the server
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch((error) => {
        console.error('Unhandled error:', error);
        process.exit(1);
    });
}
