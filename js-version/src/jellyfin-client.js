/**
 * Jellyfin API Client
 */

import axios from 'axios';

/**
 * Represents a media item from Jellyfin
 */
export class MediaItem {
    constructor(data) {
        this.id = data.Id;
        this.name = data.Name;
        this.type = data.Type;
        this.year = data.ProductionYear || null;
        this.overview = data.Overview || null;
        this.genres = data.Genres || [];
        this.runtimeTicks = data.RunTimeTicks || null;
        this.playUrl = null;
        this.streamUrl = null;
    }

    /**
     * Get runtime in minutes
     * @returns {number|null} Runtime in minutes
     */
    getRuntimeMinutes() {
        if (!this.runtimeTicks) return null;
        return Math.floor(this.runtimeTicks / 600000000); // Convert ticks to minutes
    }
}

/**
 * Client for interacting with Jellyfin API
 */
export class JellyfinClient {
    constructor(config) {
        this.config = config;
        this.axios = axios.create({
            timeout: 30000,
            headers: {
                'X-Emby-Token': config.apiKey,
                'Content-Type': 'application/json'
            }
        });
    }

    /**
     * Build full URL for API endpoint
     * @param {string} endpoint - API endpoint
     * @returns {string} Full URL
     */
    buildUrl(endpoint) {
        const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
        return `${this.config.serverUrl}/${cleanEndpoint}`;
    }

    /**
     * Search for media items by name
     * @param {string} query - Search query
     * @param {string[]} mediaTypes - Media types to search for
     * @returns {Promise<MediaItem[]>} Array of media items
     */
    async searchMedia(query, mediaTypes = ['Movie', 'Series']) {
        const params = {
            searchTerm: query,
            IncludeItemTypes: mediaTypes.join(','),
            Limit: 20,
            Recursive: true,
            EnableTotalRecordCount: false
        };

        const url = this.buildUrl('/Items');

        try {
            console.log(`Searching Jellyfin: ${url} with params:`, params);

            const response = await this.axios.get(url, { params });

            console.log(`Jellyfin response status: ${response.status}`);
            console.log(`Jellyfin response keys: ${Object.keys(response.data)}`);

            const items = response.data.Items || [];
            const mediaItems = items.map(item => {
                const mediaItem = new MediaItem(item);
                mediaItem.playUrl = this.getPlayUrl(item.Id);
                mediaItem.streamUrl = this.getStreamUrl(item.Id);
                return mediaItem;
            });

            return mediaItems;

        } catch (error) {
            console.error('HTTP error searching media:', error.message);
            throw error;
        }
    }

    /**
     * Get detailed information about a specific media item
     * @param {string} itemId - Item ID
     * @returns {Promise<MediaItem|null>} Media item or null if not found
     */
    async getMediaDetails(itemId) {
        const params = {
            Fields: 'Overview,Genres,RunTimeTicks,MediaSources'
        };

        const url = this.buildUrl(`/Items/${itemId}`);

        try {
            const response = await this.axios.get(url, { params });

            const mediaItem = new MediaItem(response.data);
            mediaItem.playUrl = this.getPlayUrl(response.data.Id);
            mediaItem.streamUrl = this.getStreamUrl(response.data.Id);

            return mediaItem;

        } catch (error) {
            if (error.response?.status === 404) {
                console.error('Media item not found:', itemId);
                return null;
            }
            console.error('HTTP error getting media details:', error.message);
            throw error;
        }
    }

    /**
     * Generate play URL for a media item
     * @param {string} itemId - Item ID
     * @returns {string} Play URL
     */
    getPlayUrl(itemId) {
        return `${this.config.serverUrl}/web/index.html#!/details?id=${itemId}`;
    }

    /**
     * Generate direct stream URL for a media item
     * @param {string} itemId - Item ID
     * @returns {string} Stream URL
     */
    getStreamUrl(itemId) {
        const baseUrl = `${this.config.serverUrl}/Videos/${itemId}/stream`;

        const params = [
            `api_key=${this.config.apiKey}`,
            'Container=mp4,mkv,avi,mov,wmv,flv,webm',
            'VideoCodec=h264,hevc,vp8,vp9',
            'AudioCodec=aac,mp3,ac3,eac3,flac,vorbis'
        ];

        return `${baseUrl}?${params.join('&')}`;
    }
}
