# Jellyfin MCP Server (JavaScript Version)

一个用于查询 Jellyfin 媒体服务器的 Model Context Protocol (MCP) 服务器的 JavaScript 实现。该服务器提供了搜索电影和电视剧的工具，并返回播放 URL 和流媒体 URL。

## 功能特性

- 🎬 搜索电影
- 📺 搜索电视剧
- 🔍 搜索所有媒体类型
- 📋 获取媒体详细信息
- 🔗 生成播放 URL 和直接流媒体 URL
- 🔄 支持 STDIO 模式的 MCP 协议
- ⚡ 基于 Node.js 的高性能异步处理

## 系统要求

- Node.js >= 18.0.0
- npm 或 yarn 包管理器

## 安装

### 克隆项目并安装依赖

```bash
git clone <repository-url>
cd jellyfin-mcp/js-version
npm install
```

### 全局安装

```bash
npm install -g .
```

## 配置

### 环境变量

创建 `.env` 文件或设置以下环境变量：

```bash
# 必需：Jellyfin 服务器 URL
export JELLYFIN_SERVER_URL="http://localhost:8096"

# 必需：Jellyfin API 密钥
export JELLYFIN_API_KEY="your_api_key_here"
```

### 获取 Jellyfin API 密钥

1. 登录到 Jellyfin 管理面板
2. 导航到 **Dashboard** > **API Keys**
3. 点击 **+** 创建新的 API 密钥
4. 复制生成的 API 密钥

## 使用方法

### 使用启动脚本（推荐）

```bash
npm start
```

### 开发模式（自动重启）

```bash
npm run dev
```

### 直接运行服务器

```bash
npm run direct
```

### 使用全局安装的命令

```bash
jellyfin-mcp-js
```

## MCP 客户端配置

### Claude Desktop 配置

在 Claude Desktop 的配置文件中添加以下内容：

```json
{
  "mcpServers": {
    "jellyfin-js": {
      "command": "node",
      "args": ["/path/to/jellyfin-mcp/js-version/run_server.js"],
      "env": {
        "JELLYFIN_SERVER_URL": "http://localhost:8096",
        "JELLYFIN_API_KEY": "your_api_key_here"
      }
    }
  }
}
```

**注意：** 请将路径替换为实际的文件路径。

## 可用工具

### 1. search_movies
搜索电影

**参数：**
- `query` (string): 电影标题搜索查询

**示例：**
```json
{
  "name": "search_movies",
  "arguments": {
    "query": "The Matrix"
  }
}
```

### 2. search_tv_shows
搜索电视剧

**参数：**
- `query` (string): 电视剧标题搜索查询

**示例：**
```json
{
  "name": "search_tv_shows",
  "arguments": {
    "query": "Breaking Bad"
  }
}
```

### 3. search_all_media
搜索所有媒体类型（电影和电视剧）

**参数：**
- `query` (string): 媒体标题搜索查询

**示例：**
```json
{
  "name": "search_all_media",
  "arguments": {
    "query": "Star Wars"
  }
}
```

### 4. get_media_details
获取特定媒体项目的详细信息

**参数：**
- `item_id` (string): Jellyfin 项目 ID

**示例：**
```json
{
  "name": "get_media_details",
  "arguments": {
    "item_id": "abc123def456"
  }
}
```

## 项目结构

```
js-version/
├── src/
│   ├── config.js           # 配置管理
│   ├── jellyfin-client.js  # Jellyfin API 客户端
│   └── server.js           # MCP 服务器主文件
├── test/
│   └── test.js             # 测试文件
├── .env.example            # 环境变量示例
├── package.json            # 项目配置
└── README.md               # 项目文档
```

## 技术栈

- **Node.js**: JavaScript 运行时
- **@modelcontextprotocol/sdk**: MCP 协议 SDK
- **axios**: HTTP 客户端
- **dotenv**: 环境变量管理

## 与 Python 版本的差异

### 优势
- **性能**: Node.js 的事件循环提供了优秀的异步性能
- **内存占用**: 相比 Python 版本更低的内存占用
- **启动速度**: 更快的启动时间
- **生态系统**: 丰富的 npm 包生态

### 功能对等
- 完全兼容的 MCP 协议实现
- 相同的 Jellyfin API 调用
- 一致的工具接口和响应格式
- 相同的配置选项

## 故障排除

### 常见问题

1. **模块导入错误**
   - 确保使用 Node.js >= 18.0.0
   - 检查 package.json 中的 `"type": "module"` 配置

2. **连接错误**
   - 确保 Jellyfin 服务器正在运行且可访问
   - 检查 `JELLYFIN_SERVER_URL` 是否正确

3. **认证错误**
   - 验证 `JELLYFIN_API_KEY` 是否有效
   - 确保 API 密钥具有适当的权限

4. **搜索结果为空**
   - 检查搜索查询是否正确
   - 确保 Jellyfin 服务器中有相应的媒体内容

### 调试模式

设置环境变量启用详细日志：

```bash
DEBUG=* npm start
```

## 开发

### 运行测试

```bash
npm test
```

### 代码风格

项目使用 ES6+ 语法和模块系统，遵循现代 JavaScript 最佳实践。

## 许可证

MIT License

## 贡献

欢迎提交 Pull Request 和 Issue！
