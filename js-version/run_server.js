#!/usr/bin/env node

/**
 * Jellyfin MCP Server Runner (JavaScript Version)
 *
 * This script provides an easy way to run the Jellyfin MCP server with proper error handling.
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync, readFileSync } from 'fs';
import { loadConfig } from './src/config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Load environment variables from .env file if it exists
 */
function loadEnvFile() {
    const envFile = join(__dirname, '.env');
    if (existsSync(envFile)) {
        console.log(`Loading environment from ${envFile}`);
        const envContent = readFileSync(envFile, 'utf8');

        for (const line of envContent.split('\n')) {
            const trimmedLine = line.trim();
            if (trimmedLine && !trimmedLine.startsWith('#') && trimmedLine.includes('=')) {
                const [key, ...valueParts] = trimmedLine.split('=');
                const value = valueParts.join('=');
                process.env[key.trim()] = value.trim();
            }
        }
    }
}

/**
 * Check if required configuration is present
 */
function checkConfig() {
    const requiredVars = ['JELLYFIN_SERVER_URL', 'JELLYFIN_API_KEY'];
    const missingVars = [];

    for (const varName of requiredVars) {
        if (!process.env[varName]) {
            missingVars.push(varName);
        }
    }

    if (missingVars.length > 0) {
        console.log('❌ Missing required environment variables:');
        for (const varName of missingVars) {
            console.log(`   - ${varName}`);
        }
        console.log('\nPlease set these variables or create a .env file based on .env.example');
        return false;
    }

    console.log('✅ Configuration looks good!');
    console.log(`   Server URL: ${process.env.JELLYFIN_SERVER_URL}`);
    return true;
}

/**
 * Main entry point
 */
async function main() {
    console.log('🎬 Jellyfin MCP Server (JavaScript Version)');
    console.log('='.repeat(50));

    // Load .env file if it exists
    loadEnvFile();

    // Check configuration
    if (!checkConfig()) {
        process.exit(1);
    }

    console.log('\n🚀 Starting MCP server...');
    console.log('Press Ctrl+C to stop the server');
    console.log('-'.repeat(50));

    try {
        // Import and run the server
        const { main: serverMain } = await import('./src/server.js');
        await serverMain();
    } catch (error) {
        if (error.message.includes('environment variable')) {
            console.error('\n❌ Configuration error:', error.message);
        } else {
            console.error('\n❌ Server error:', error.message);
        }
        process.exit(1);
    }
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n\n👋 Server stopped by user');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n\n👋 Server terminated');
    process.exit(0);
});

// Start the server
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch((error) => {
        console.error('\n❌ Unhandled error:', error);
        process.exit(1);
    });
}
