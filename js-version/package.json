{"name": "jellyfin-mcp-js", "version": "0.1.0", "description": "MCP server for querying Jellyfin media server (JavaScript version)", "main": "src/server.js", "type": "module", "scripts": {"start": "node run_server.js", "dev": "node --watch run_server.js", "direct": "node src/server.js", "test": "node test/test.js"}, "bin": {"jellyfin-mcp-js": "./src/server.js"}, "keywords": ["jellyfin", "mcp", "media", "server", "model-context-protocol"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "axios": "^1.6.0", "dotenv": "^16.3.0"}, "devDependencies": {"@types/node": "^20.0.0"}, "engines": {"node": ">=18.0.0"}}