#!/usr/bin/env node

/**
 * Simple test script for Jellyfin MCP Server (JavaScript Version)
 */

import { loadConfig } from '../src/config.js';
import { JellyfinClient } from '../src/jellyfin-client.js';

async function testJellyfinConnection() {
    console.log('🧪 Testing Jellyfin MCP Server (JavaScript Version)');
    console.log('=' * 60);

    try {
        // Test configuration loading
        console.log('\n📋 Testing configuration loading...');
        const config = loadConfig();
        console.log('✅ Configuration loaded successfully');
        console.log(`   Server URL: ${config.serverUrl}`);

        // Test Jellyfin client initialization
        console.log('\n🔧 Testing Jellyfin client initialization...');
        const client = new JellyfinClient(config);
        console.log('✅ Jellyfin client initialized successfully');

        // Test basic search functionality
        console.log('\n🔍 Testing search functionality...');
        try {
            const results = await client.searchMedia('test', ['Movie']);
            console.log(`✅ Search completed successfully (found ${results.length} results)`);

            if (results.length > 0) {
                const firstResult = results[0];
                console.log(`   First result: ${firstResult.name} (${firstResult.type})`);
                console.log(`   Play URL: ${firstResult.playUrl}`);
                console.log(`   Stream URL: ${firstResult.streamUrl.substring(0, 100)}...`);
            }
        } catch (error) {
            if (error.response?.status === 400) {
                console.log('⚠️  Search returned 400 (expected for test query)');
            } else {
                throw error;
            }
        }

        console.log('\n🎉 All tests passed!');
        console.log('\n📝 Next steps:');
        console.log('1. Set up your Jellyfin server URL and API key in .env file');
        console.log('2. Run: npm start');
        console.log('3. Configure your MCP client to use this server');

        return true;

    } catch (error) {
        console.error('\n❌ Test failed:', error.message);

        if (error.message.includes('environment variable')) {
            console.log('\n💡 Tip: Copy .env.example to .env and fill in your Jellyfin details');
        }

        return false;
    }
}

// Run tests
if (import.meta.url === `file://${process.argv[1]}`) {
    testJellyfinConnection()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Unhandled error:', error);
            process.exit(1);
        });
}
