# Jellyfin MCP Server 技术文档

## 项目概述

Jellyfin MCP Server 是一个基于 Model Context Protocol (MCP) 的服务器应用，用于与 Jellyfin 媒体服务器进行交互。它提供了搜索电影、电视剧和获取媒体详细信息的功能，并生成播放和流媒体 URL。

## 核心功能

### 1. 媒体搜索功能
- **搜索电影** (`search_movies`): 在 Jellyfin 服务器中搜索电影
- **搜索电视剧** (`search_tv_shows`): 在 Jellyfin 服务器中搜索电视剧/系列
- **搜索所有媒体** (`search_all_media`): 同时搜索电影和电视剧
- **获取媒体详情** (`get_media_details`): 根据媒体 ID 获取详细信息

### 2. URL 生成功能
- **播放 URL**: 生成在 Jellyfin Web 界面中播放媒体的 URL
- **流媒体 URL**: 生成直接流媒体播放的 URL，包含编解码器参数

## 技术架构

### 架构模式
- **MCP 协议**: 使用 Model Context Protocol 进行客户端-服务器通信
- **STDIO 模式**: 通过标准输入输出进行通信
- **异步编程**: 使用 Python asyncio 进行异步处理

### 核心组件

#### 1. JellyfinConfig
```python
class JellyfinConfig(BaseModel):
    server_url: str  # Jellyfin 服务器 URL
    api_key: str     # Jellyfin API 密钥
```

#### 2. MediaItem
```python
class MediaItem(BaseModel):
    id: str                    # 媒体 ID
    name: str                  # 媒体名称
    type: str                  # 媒体类型 (Movie/Series)
    year: Optional[int]        # 发行年份
    overview: Optional[str]    # 简介
    genres: List[str]          # 类型标签
    runtime_ticks: Optional[int] # 运行时长
    play_url: Optional[str]    # 播放 URL
    stream_url: Optional[str]  # 流媒体 URL
```

#### 3. JellyfinClient
负责与 Jellyfin API 交互的核心客户端类：
- **连接管理**: 使用 httpx.AsyncClient 进行 HTTP 请求
- **认证**: 使用 X-Emby-Token 头进行 API 认证
- **搜索功能**: 调用 Jellyfin `/Items` API 进行媒体搜索
- **URL 构建**: 生成播放和流媒体 URL

## API 接口设计

### Jellyfin API 调用
```http
GET /Items?searchTerm={query}&IncludeItemTypes={types}&Limit=20&Recursive=true
Headers:
  X-Emby-Token: {api_key}
  Content-Type: application/json
```

### MCP 工具接口

#### search_movies
```json
{
  "name": "search_movies",
  "arguments": {
    "query": "电影名称"
  }
}
```

#### search_tv_shows
```json
{
  "name": "search_tv_shows",
  "arguments": {
    "query": "电视剧名称"
  }
}
```

#### search_all_media
```json
{
  "name": "search_all_media",
  "arguments": {
    "query": "媒体名称"
  }
}
```

#### get_media_details
```json
{
  "name": "get_media_details",
  "arguments": {
    "item_id": "媒体ID"
  }
}
```

## 配置管理

### 环境变量
- `JELLYFIN_SERVER_URL`: Jellyfin 服务器地址 (必需)
- `JELLYFIN_API_KEY`: Jellyfin API 密钥 (必需)

### 配置文件
支持 `.env` 文件配置，格式如下：
```env
JELLYFIN_SERVER_URL=https://your-jellyfin-server.com
JELLYFIN_API_KEY=your_api_key_here
```

## 错误处理

### HTTP 错误处理
- **400 Bad Request**: 参数错误或 API 调用格式错误
- **401 Unauthorized**: API 密钥无效
- **404 Not Found**: 媒体项目不存在
- **503 Service Unavailable**: Jellyfin 服务器不可用

### 应用层错误处理
- **配置错误**: 缺少必需的环境变量
- **网络错误**: HTTP 请求超时或连接失败
- **数据解析错误**: JSON 响应格式错误

## 日志系统

使用 Python logging 模块，记录以下信息：
- 配置加载状态
- API 请求和响应
- 错误信息和堆栈跟踪
- 服务器启动和关闭事件

## 部署方式

### 直接运行
```bash
python run_server.py
```

### MCP 客户端集成
```json
{
  "mcpServers": {
    "jellyfin": {
      "command": "python",
      "args": ["/path/to/jellyfin-mcp/run_server.py"],
      "env": {
        "JELLYFIN_SERVER_URL": "https://your-server.com",
        "JELLYFIN_API_KEY": "your_api_key"
      }
    }
  }
}
```

## 性能特性

- **异步处理**: 使用 asyncio 提高并发性能
- **连接复用**: httpx.AsyncClient 复用 HTTP 连接
- **超时控制**: 30秒 HTTP 请求超时
- **资源清理**: 自动关闭 HTTP 客户端连接

## 安全考虑

- **API 密钥保护**: 通过环境变量传递敏感信息
- **输入验证**: 使用 Pydantic 进行数据验证
- **错误信息过滤**: 避免在错误响应中泄露敏感信息

## 扩展性

- **模块化设计**: 核心功能分离，易于扩展
- **配置灵活**: 支持多种配置方式
- **工具可扩展**: 易于添加新的 MCP 工具
- **API 兼容**: 支持 Jellyfin API 的不同版本
